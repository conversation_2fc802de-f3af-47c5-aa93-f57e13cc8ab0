// 集团管控AES加解密系统 - SecureVault by lyg
// JavaScript实现，与Java版本保持兼容

// 密钥字符串（与Java版本保持一致）
const KEY_STRING = "BUYHg67f@WDC1qfv4raz#ESXm89nPOKL";

/**
 * 生成与Java版本兼容的AES密钥
 * 使用Java测试程序得到的确切密钥字节
 */
function generateKey() {
    // 从Java测试程序得到的密钥字节: 51 e7 64 b9 7a 4f 88 f4 9e b5 0e 42 9e 59 95 8e
    const keyBytes = [
        0x51, 0xe7, 0x64, 0xb9, 0x7a, 0x4f, 0x88, 0xf4,
        0x9e, 0xb5, 0x0e, 0x42, 0x9e, 0x59, 0x95, 0x8e
    ];

    // 将字节数组转换为CryptoJS WordArray
    const words = [];
    for (let i = 0; i < 16; i += 4) {
        const word = (keyBytes[i] << 24) | (keyBytes[i + 1] << 16) | (keyBytes[i + 2] << 8) | keyBytes[i + 3];
        words.push(word);
    }

    return CryptoJS.lib.WordArray.create(words, 16);
}

/**
 * 加密文本
 * @param {string} plaintext - 要加密的明文
 * @returns {string} Base64编码的加密结果
 */
function encrypt(plaintext) {
    try {
        if (!plaintext || plaintext.trim() === '') {
            throw new Error('输入不能为空');
        }

        // 生成密钥
        const key = generateKey();
        
        // 生成随机初始化向量（16字节）
        const iv = CryptoJS.lib.WordArray.random(16);
        
        // 使用AES-CBC模式加密
        const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });
        
        // 将IV和加密数据合并
        const combined = iv.concat(encrypted.ciphertext);
        
        // 返回Base64编码的结果
        return CryptoJS.enc.Base64.stringify(combined);
    } catch (error) {
        throw new Error('加密失败: ' + error.message);
    }
}

/**
 * 解密文本
 * @param {string} ciphertext - 要解密的Base64编码密文
 * @returns {string} 解密后的明文
 */
function decrypt(ciphertext) {
    try {
        if (!ciphertext || ciphertext.trim() === '') {
            throw new Error('输入不能为空');
        }

        // 生成密钥
        const key = generateKey();
        
        // Base64解码
        const combined = CryptoJS.enc.Base64.parse(ciphertext);
        
        // 分离IV（前16字节）和加密数据
        const iv = CryptoJS.lib.WordArray.create(combined.words.slice(0, 4)); // 16字节 = 4个32位字
        const encryptedData = CryptoJS.lib.WordArray.create(combined.words.slice(4));
        
        // 使用AES-CBC模式解密
        const decrypted = CryptoJS.AES.decrypt(
            CryptoJS.lib.CipherParams.create({
                ciphertext: encryptedData
            }), 
            key, 
            {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            }
        );
        
        // 转换为UTF-8字符串
        const result = decrypted.toString(CryptoJS.enc.Utf8);
        
        if (!result) {
            throw new Error('解密失败，请检查密文格式是否正确');
        }
        
        return result;
    } catch (error) {
        throw new Error('解密失败: ' + error.message);
    }
}

// UI控制函数

// 当前操作模式：'encrypt' 或 'decrypt'
let currentMode = 'encrypt';

// 存储当前的加密结果
let currentEncryptedResult = '';

/**
 * 设置操作模式
 */
function setMode(mode) {
    currentMode = mode;

    // 更新按钮状态
    const encryptBtn = document.getElementById('encryptModeBtn');
    const decryptBtn = document.getElementById('decryptModeBtn');

    if (mode === 'encrypt') {
        encryptBtn.classList.add('active');
        decryptBtn.classList.remove('active');

        // 更新界面文本
        document.getElementById('operationTitle').innerHTML = '🔒 文本加密';
        document.getElementById('inputLabel').textContent = '请输入要加密的文本:';
        document.getElementById('processBtn').textContent = '加密';
        document.getElementById('inputText').placeholder = '在此输入需要加密的文本内容...';
    } else {
        decryptBtn.classList.add('active');
        encryptBtn.classList.remove('active');

        // 更新界面文本
        document.getElementById('operationTitle').innerHTML = '🔓 文本解密';
        document.getElementById('inputLabel').textContent = '请输入要解密的文本:';
        document.getElementById('processBtn').textContent = '解密';
        document.getElementById('inputText').placeholder = '在此输入需要解密的密文内容...';
    }

    // 清空输入和结果
    clearInput();

    // 根据模式显示或隐藏链接生成区域
    if (mode === 'decrypt') {
        hideLinkGeneration();
    }

    // 聚焦到输入框
    setTimeout(() => {
        document.getElementById('inputText').focus();
    }, 100);
}

/**
 * 根据当前模式执行相应操作
 */
function processText() {
    const inputText = document.getElementById('inputText').value;

    if (currentMode === 'encrypt') {
        try {
            const result = encrypt(inputText);
            currentEncryptedResult = result; // 保存加密结果
            showResult('result', result, true, '加密成功！');
            // 加密成功后显示链接生成区域
            showLinkGeneration();
        } catch (error) {
            currentEncryptedResult = ''; // 清空加密结果
            showResult('result', error.message, false, '加密失败');
            // 加密失败时隐藏链接生成区域
            hideLinkGeneration();
        }
    } else {
        try {
            const result = decrypt(inputText);
            currentEncryptedResult = ''; // 解密模式下清空加密结果
            showResult('result', result, true, '解密成功！');
            // 解密模式下隐藏链接生成区域
            hideLinkGeneration();
        } catch (error) {
            currentEncryptedResult = ''; // 清空加密结果
            showResult('result', error.message, false, '解密失败');
            hideLinkGeneration();
        }
    }
}

/**
 * 清空输入
 */
function clearInput() {
    document.getElementById('inputText').value = '';
    currentEncryptedResult = ''; // 清空加密结果
    hideResult('result');
    hideLinkGeneration();
    document.getElementById('inputText').focus();
}

/**
 * 显示链接生成区域
 */
function showLinkGeneration() {
    document.getElementById('linkGeneration').classList.add('show');
    // 隐藏之前的链接结果
    document.getElementById('pcLinkResult').classList.remove('show');
    document.getElementById('mobileLinkResult').classList.remove('show');
}

/**
 * 隐藏链接生成区域
 */
function hideLinkGeneration() {
    document.getElementById('linkGeneration').classList.remove('show');
    document.getElementById('pcLinkResult').classList.remove('show');
    document.getElementById('mobileLinkResult').classList.remove('show');
}

/**
 * 生成PC端登录链接
 */
function generatePCLink() {
    if (!currentEncryptedResult || currentEncryptedResult.trim() === '') {
        showToast('请先进行加密操作');
        return;
    }

    const pcLink = `https://fw.btsteel.com/ssoLogin?username=${currentEncryptedResult}`;

    // 显示PC链接结果
    document.getElementById('pcLinkContent').textContent = pcLink;
    document.getElementById('pcLinkResult').classList.add('show');

    // 滚动到链接结果位置
    setTimeout(() => {
        document.getElementById('pcLinkResult').scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
}

/**
 * 生成移动端登录链接
 */
function generateMobileLink() {
    if (!currentEncryptedResult || currentEncryptedResult.trim() === '') {
        showToast('请先进行加密操作');
        return;
    }

    const mobileLink = `http://fw.btsteel.com/fwh5/#/?moduleid=435046443&username=${currentEncryptedResult}`;

    // 显示移动端链接结果
    document.getElementById('mobileLinkContent').textContent = mobileLink;
    document.getElementById('mobileLinkResult').classList.add('show');

    // 滚动到链接结果位置
    setTimeout(() => {
        document.getElementById('mobileLinkResult').scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
}

/**
 * 复制链接到剪贴板
 */
function copyLink(contentId) {
    const content = document.getElementById(contentId).textContent;

    if (!content || content.trim() === '') {
        showToast('没有可复制的链接');
        return;
    }

    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代API
        navigator.clipboard.writeText(content).then(() => {
            showToast('链接复制成功！');
        }).catch(() => {
            fallbackCopy(content);
        });
    } else {
        // 降级方案
        fallbackCopy(content);
    }
}

/**
 * 显示结果
 */
function showResult(resultId, content, isSuccess, title) {
    const resultPanel = document.getElementById(resultId);
    const resultIcon = document.getElementById(resultId.replace('Result', 'ResultIcon'));
    const resultTitle = document.getElementById(resultId.replace('Result', 'ResultTitle'));
    const resultContent = document.getElementById(resultId.replace('Result', 'ResultContent'));
    
    // 设置图标和标题
    if (isSuccess) {
        resultIcon.textContent = '✅';
        resultTitle.textContent = title;
        resultPanel.className = 'result-panel success';
        resultTitle.className = 'result-title success';
    } else {
        resultIcon.textContent = '❌';
        resultTitle.textContent = title;
        resultPanel.className = 'result-panel error';
        resultTitle.className = 'result-title error';
    }
    
    // 设置内容
    resultContent.textContent = content;
    
    // 显示结果面板
    resultPanel.style.display = 'block';
    
    // 滚动到结果位置
    setTimeout(() => {
        resultPanel.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
}

/**
 * 隐藏结果
 */
function hideResult(resultId) {
    document.getElementById(resultId).style.display = 'none';
}

/**
 * 复制输入内容到剪贴板
 */
function copyInputText() {
    const inputText = document.getElementById('inputText').value;

    if (!inputText || inputText.trim() === '') {
        showToast('输入框为空，无内容可复制');
        return;
    }

    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代API
        navigator.clipboard.writeText(inputText).then(() => {
            showToast('输入内容复制成功！');
        }).catch(() => {
            fallbackCopy(inputText);
        });
    } else {
        // 降级方案
        fallbackCopy(inputText);
    }
}

/**
 * 复制结果到剪贴板
 */
function copyResult(contentId) {
    const content = document.getElementById(contentId).textContent;

    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代API
        navigator.clipboard.writeText(content).then(() => {
            showToast('结果复制成功！');
        }).catch(() => {
            fallbackCopy(content);
        });
    } else {
        // 降级方案
        fallbackCopy(content);
    }
}

/**
 * 降级复制方案
 */
function fallbackCopy(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showToast('复制成功！');
    } catch (err) {
        showToast('复制失败，请手动复制');
    }
    
    document.body.removeChild(textArea);
}

/**
 * 显示提示消息
 */
function showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #333;
        color: white;
        padding: 12px 20px;
        border-radius: 5px;
        z-index: 1000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后移除
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 键盘快捷键支持
document.addEventListener('keydown', function(event) {
    // Ctrl+Enter 或 Cmd+Enter 执行操作
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        processText();
    }

    // 只有在不是输入框焦点时，数字键1和2才切换模式
    const activeElement = document.activeElement;
    const isInputFocused = activeElement && (activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'INPUT');

    if (!isInputFocused) {
        if (event.key === '1') {
            setMode('encrypt');
        } else if (event.key === '2') {
            setMode('decrypt');
        }
    }
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化为加密模式
    setMode('encrypt');

    // 修复emoji显示问题
    document.getElementById('operationTitle').innerHTML = '🔒 文本加密';

    // 添加输入框的键盘快捷键支持
    const textarea = document.getElementById('inputText');
    textarea.addEventListener('keydown', function(event) {
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            processText();
        }
        // Ctrl+D 或 Cmd+D 复制输入内容
        if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
            event.preventDefault();
            copyInputText();
        }
    });
});
