<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字符串转换工具 - 多行转逗号分隔</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            color: #333;
        }
        
        .container {
            width: 100%;
            max-width: 800px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(to right, #2575fc, #6a11cb);
            color: white;
            padding: 25px 30px;
            text-align: center;
        }
        
        h1 {
            font-size: 2.2rem;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        
        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .content {
            padding: 30px;
        }
        
        .input-area, .output-area {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.1rem;
        }
        
        textarea {
            width: 100%;
            height: 150px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            resize: vertical;
            transition: border-color 0.3s;
        }
        
        textarea:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .input-area textarea {
            background-color: #f8f9fa;
        }
        
        .output-area textarea {
            background-color: #e8f4fc;
        }
        
        .buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin: 25px 0;
        }
        
        button {
            flex: 1;
            min-width: 140px;
            padding: 14px 20px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .convert-btn {
            background: linear-gradient(to right, #2ecc71, #27ae60);
            color: white;
        }
        
        .copy-btn {
            background: linear-gradient(to right, #3498db, #2980b9);
            color: white;
        }
        
        .reverse-btn {
            background: linear-gradient(to right, #9b59b6, #8e44ad);
            color: white;
        }
        
        .clear-btn {
            background: linear-gradient(to right, #e74c3c, #c0392b);
            color: white;
        }
        
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        button:active {
            transform: translateY(1px);
        }
        
        .examples {
            background-color: #f1f8ff;
            border-left: 4px solid #3498db;
            padding: 18px;
            border-radius: 0 8px 8px 0;
            margin-top: 25px;
        }
        
        .examples h3 {
            margin-bottom: 12px;
            color: #2c3e50;
        }
        
        .examples p {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        
        .example-input {
            font-family: monospace;
            background-color: #eaf6ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .example-output {
            font-family: monospace;
            background-color: #e0f0ff;
            padding: 10px;
            border-radius: 5px;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-size: 0.9rem;
            border-top: 1px solid #eee;
        }
        
        @media (max-width: 600px) {
            .buttons {
                flex-direction: column;
            }
            
            h1 {
                font-size: 1.8rem;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>字符串转换工具</h1>
            <div class="subtitle">多行文本转逗号分隔格式</div>
        </header>
        
        <div class="content">
            <div class="input-area">
                <label for="inputText">输入文本（支持换行和空格分隔）：</label>
                <textarea id="inputText" placeholder="请输入文本"></textarea>
            </div>
            
            <div class="buttons">
                <button class="convert-btn" onclick="convert()">
                    <i class="fas fa-exchange-alt"></i> 转换为逗号分隔
                </button>
                <button class="copy-btn" onclick="copyOutput()">
                    <i class="fas fa-copy"></i> 复制结果
                </button>
                <button class="reverse-btn" onclick="reverseConversion()">
                    <i class="fas fa-undo"></i> 反向转换
                </button>
                <button class="clear-btn" onclick="clearAll()">
                    <i class="fas fa-trash"></i> 清空内容
                </button>
            </div>
            
            <div class="output-area">
                <label for="outputText">转换结果：</label>
                <textarea id="outputText" placeholder="转换结果将显示在这里" readonly></textarea>
            </div>

        </div>
        
        <footer>
            <p>© 2025 字符串转换工具 | 多行转逗号分隔格式 BY lyg</p>
        </footer>
    </div>
    
    <!-- 引入FontAwesome图标 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>
    
    <script>
        function convert() {
            const inputText = document.getElementById('inputText').value;

            if (!inputText.trim()) {
                document.getElementById('outputText').value = '';
                return;
            }

            // 统一处理：将所有空白字符（包括换行、空格、制表符等）作为分隔符
            // 使用正则表达式 \s+ 匹配一个或多个空白字符
            const items = inputText.trim().split(/\s+/).filter(item => item.length > 0);
            const result = items.join(',');

            document.getElementById('outputText').value = result;
        }
        
        function reverseConversion() {
            const inputText = document.getElementById('inputText').value;
            
            // 尝试按逗号分割
            let items = inputText.split(',');
            
            // 如果分割后只有一项，尝试按空格分割
            if (items.length === 1) {
                items = inputText.split(/\s+/);
            }
            
            // 过滤空项并去除每项首尾空格
            const nonEmptyItems = items
                .map(item => item.trim())
                .filter(item => item.length > 0);
            
            // 用换行符连接
            const result = nonEmptyItems.join('\n');
            
            document.getElementById('outputText').value = result;
        }
        
        function copyOutput() {
            const outputText = document.getElementById('outputText');
            outputText.select();
            document.execCommand('copy');
            
            // 显示复制成功的视觉反馈
            const copyBtn = document.querySelector('.copy-btn');
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制!';
            
            setTimeout(() => {
                copyBtn.innerHTML = originalText;
            }, 2000);
        }
        
        function clearAll() {
            document.getElementById('inputText').value = '';
            document.getElementById('outputText').value = '';
        }
        
        // 页面加载时添加示例文本
        window.onload = function() {
            document.getElementById('inputText').value =
                "";
        };
    </script>
</body>
</html>